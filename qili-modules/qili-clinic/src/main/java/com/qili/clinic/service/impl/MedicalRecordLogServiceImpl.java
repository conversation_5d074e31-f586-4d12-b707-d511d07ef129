package com.qili.clinic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.MedicalRecordLog;
import com.qili.clinic.domain.bo.MedicalRecordLogBo;
import com.qili.clinic.domain.vo.MedicalRecordLogVo;
import com.qili.clinic.mapper.MedicalRecordLogMapper;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 病历状态变更日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalRecordLogServiceImpl implements IMedicalRecordLogService {

    private final MedicalRecordLogMapper baseMapper;

    /**
     * 查询病历状态变更日志
     *
     * @param id 主键
     * @return 病历状态变更日志
     */
    @Override
    public MedicalRecordLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询病历状态变更日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历状态变更日志分页列表
     */
    @Override
    public TableDataInfo<MedicalRecordLogVo> queryPageList(MedicalRecordLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalRecordLog> lqw = buildQueryWrapper(bo);
        Page<MedicalRecordLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历状态变更日志列表
     *
     * @param bo 查询条件
     * @return 病历状态变更日志列表
     */
    @Override
    public List<MedicalRecordLogVo> queryList(MedicalRecordLogBo bo) {
        LambdaQueryWrapper<MedicalRecordLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MedicalRecordLog> buildQueryWrapper(MedicalRecordLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalRecordLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MedicalRecordLog::getId);
        lqw.eq(bo.getRecordId() != null, MedicalRecordLog::getRecordId, bo.getRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getAction()), MedicalRecordLog::getAction, bo.getAction());
        lqw.like(StringUtils.isNotBlank(bo.getActionName()), MedicalRecordLog::getActionName, bo.getActionName());
        lqw.eq(bo.getActionTime() != null, MedicalRecordLog::getActionTime, bo.getActionTime());
        return lqw;
    }

    /**
     * 新增病历状态变更日志
     *
     * @param bo 病历状态变更日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MedicalRecordLogBo bo) {
        MedicalRecordLog add = MapstructUtils.convert(bo, MedicalRecordLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Async
    @Override
    public void insertAsync(MedicalRecordLogBo bo) {
        insertByBo(bo);
        log.info("异步新增病历状态变更日志成功,记录ID:{}", bo.getRecordId());
    }

    /**
     * 修改病历状态变更日志
     *
     * @param bo 病历状态变更日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MedicalRecordLogBo bo) {
        MedicalRecordLog update = MapstructUtils.convert(bo, MedicalRecordLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalRecordLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除病历状态变更日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<MedicalRecordLogVo> queryListByRecordId(Long recordId) {
        LambdaQueryWrapper<MedicalRecordLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(MedicalRecordLog::getRecordId, recordId);
        lqw.orderByDesc(MedicalRecordLog::getId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Map<Long, List<MedicalRecordLogVo>> queryMapByRecordIds(Collection<Long> recordIds) {

        LambdaQueryWrapper<MedicalRecordLog> lqw = Wrappers.lambdaQuery();
        lqw.in(MedicalRecordLog::getRecordId, recordIds);
        lqw.orderByDesc(MedicalRecordLog::getId);
        List<MedicalRecordLogVo> list = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(list)) {
            return Map.of();
        }
        return list.stream().collect(Collectors.groupingBy(MedicalRecordLogVo::getRecordId));
    }
}
