package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.MedicalRecord;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 病历/就诊记录业务对象 qili_medical_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalRecord.class)
public class MedicalRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 诊号
     */
    private String visitNo;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空", groups = {AddGroup.class})
    private Long patientId;

    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空", groups = {AddGroup.class})
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 就诊时间
     */
    private Date visitTime;

    /**
     * 诊断数据
     */
    private DiagnosisDataBo diagnosisData;

    /**
     * 中药饮片处方数据
     */
    private TcmPrescriptionDataBo tcmPrescriptionData;

    /**
     * 中成药处方数据
     */
    private MedicinePrescriptionDataBo tcmpPrescriptionData;

    /**
     * 西药处方数据
     */
    private MedicinePrescriptionDataBo wmPrescriptionData;

    /**
     * 诊疗项目数据
     */
    private TreatmentProjectDataBo treatmentProjectData;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付记录
     */
    private PaymentBo payment;

}
