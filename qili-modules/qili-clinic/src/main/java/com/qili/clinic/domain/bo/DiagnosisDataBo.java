package com.qili.clinic.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * 诊断数据业务对象
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class DiagnosisDataBo {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板类型
     */
    private String type;

    /**
     * 是否默认模板
     */
    private String defaultFlag;

    /**
     * 所属目录
     */
    private String directory;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 字段数据列表
     */
    private List<DiagnosisFieldData> fieldList;

}
