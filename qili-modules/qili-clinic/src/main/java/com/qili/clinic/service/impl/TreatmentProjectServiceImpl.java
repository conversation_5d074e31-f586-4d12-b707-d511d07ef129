package com.qili.clinic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qili.clinic.domain.bo.TreatmentProjectBo;
import com.qili.clinic.domain.vo.TreatmentProjectVo;
import com.qili.clinic.domain.TreatmentProject;
import com.qili.clinic.mapper.TreatmentProjectMapper;
import com.qili.clinic.service.ITreatmentProjectService;

import java.util.*;
import java.util.Collection;

/**
 * 诊疗项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TreatmentProjectServiceImpl implements ITreatmentProjectService {

    private final TreatmentProjectMapper baseMapper;

    /**
     * 查询诊疗项目
     *
     * @param id 主键
     * @return 诊疗项目
     */
    @Override
    public TreatmentProjectVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询诊疗项目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 诊疗项目分页列表
     */
    @Override
    public TableDataInfo<TreatmentProjectVo> queryPageList(TreatmentProjectBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TreatmentProject> lqw = buildQueryWrapper(bo);
        Page<TreatmentProjectVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的诊疗项目列表
     *
     * @param bo 查询条件
     * @return 诊疗项目列表
     */
    @Override
    public List<TreatmentProjectVo> queryList(TreatmentProjectBo bo) {
        LambdaQueryWrapper<TreatmentProject> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TreatmentProject> buildQueryWrapper(TreatmentProjectBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TreatmentProject> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(TreatmentProject::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), TreatmentProject::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), TreatmentProject::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), TreatmentProject::getCategory, bo.getCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TreatmentProject::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增诊疗项目
     *
     * @param bo 诊疗项目
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(TreatmentProjectBo bo) {
        TreatmentProject add = MapstructUtils.convert(bo, TreatmentProject.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改诊疗项目
     *
     * @param bo 诊疗项目
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(TreatmentProjectBo bo) {
        TreatmentProject update = MapstructUtils.convert(bo, TreatmentProject.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TreatmentProject entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除诊疗项目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<TreatmentProjectVo> queryByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoByIds(ids);
    }
}
