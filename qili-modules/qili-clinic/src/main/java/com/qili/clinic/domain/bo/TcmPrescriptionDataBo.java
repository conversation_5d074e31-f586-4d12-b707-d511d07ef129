package com.qili.clinic.domain.bo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 中药饮片处方数据业务对象
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class TcmPrescriptionDataBo {

    /**
     * 药品列表
     */
    private List<TcmDrugItemBo> tableData;

    /**
     * 医嘱
     */
    private String prescription;

    /**
     * 剂数
     */
    private Integer dosage;

    /**
     * 频率：tid, bid等
     */
    private String frequency;

    /**
     * 用法：水煎、冲服等
     */
    private String usage;

    /**
     * 用法值
     */
    private Integer usageValue;

    /**
     * 是否代煎：1-是，0-否
     */
    private Integer isDecoction;

    /**
     * 字典映射关系
     */
    private Map<String, String> dictMapping;
}
