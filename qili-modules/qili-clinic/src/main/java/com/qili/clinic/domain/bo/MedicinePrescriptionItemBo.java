package com.qili.clinic.domain.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 中成药/西药处方项目业务对象
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class MedicinePrescriptionItemBo {

    /**
     * 药品ID
     */
    private String id;

    /**
     * 药品名称
     */
    private String name;

    /**
     * 用法，如：口服
     */
    private String usage;

    /**
     * 频率，如：tid
     */
    private String frequency;

    /**
     * 剂量，如：2片
     */
    private String dosage;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 总量
     */
    private BigDecimal totalAmount;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 药品编码
     */
    private String code;

    /**
     * 单位
     */
    private String unit;

    /**
     * 规格，如：0.25g*12片
     */
    private String spec;

    /**
     * 小计
     */
    private BigDecimal subtotal;
}
