package com.qili.clinic.domain.bo;

import lombok.Data;

/**
 * 字段数据内部类
 */
@Data
public class DiagnosisFieldData {
    /**
     * 字段ID
     */
    private String id;

    /**
     * 字段编码
     */
    private String fieldCode;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 是否默认字段
     */
    private String defaultFlag;

    /**
     * 是否必填
     */
    private String required;

    /**
     * 备注
     */
    private String remark;

    /**
     * 行键（用于前端表格）
     */
    private String _X_ROW_KEY;

    /**
     * 字段值
     */
    private String fieldValue;
}
