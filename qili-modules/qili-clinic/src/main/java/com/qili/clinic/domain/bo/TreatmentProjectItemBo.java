package com.qili.clinic.domain.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 诊疗项目业务对象
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class TreatmentProjectItemBo {

    /**
     * 项目ID
     */
    private String id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目编码
     */
    private String code;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 单位
     */
    private String unit;

    /**
     * 次数/数量
     */
    private BigDecimal amount;

    /**
     * 预计时长（分钟）
     */
    private Integer durationMinutes;

    /**
     * 小计
     */
    private BigDecimal subtotal;
}
