package com.qili.clinic.domain.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 中药饮片项目业务对象
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class TcmDrugItemBo {

    /**
     * 药品ID
     */
    private String id;

    /**
     * 药品名称
     */
    private String name;

    /**
     * 药品编码
     */
    private String code;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private BigDecimal amount;

    /**
     * 小计
     */
    private BigDecimal subtotal;
}
