package com.qili.clinic.enums;

import com.qili.common.core.exception.ServiceException;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum PaymentMethod {
    CASH("CASH", "现金"),
    WECHAT("WECHAT", "微信"),
    ALIPAY("ALIPAY", "支付宝"),
    BANKCARD("BANK<PERSON><PERSON>", "银行卡");

    private final String code;
    private final String label;

    PaymentMethod(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static PaymentMethod getByCode(String code) {
        return Arrays.stream(PaymentMethod.values())
            .filter(method -> method.getCode().equals(code.toUpperCase()))
            .findFirst()
            .orElseThrow(() -> new ServiceException("未知的支付方式: " + code));
    }
}
