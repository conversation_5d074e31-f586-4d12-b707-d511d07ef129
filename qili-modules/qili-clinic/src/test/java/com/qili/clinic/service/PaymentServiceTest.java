package com.qili.clinic.service;

import com.qili.clinic.domain.bo.*;
import com.qili.clinic.domain.vo.DrugVo;
import com.qili.clinic.domain.vo.TreatmentProjectVo;
import com.qili.clinic.service.impl.PaymentServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PaymentService 测试类
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@ExtendWith(MockitoExtension.class)
class PaymentServiceTest {

    @Mock
    private IDrugService drugService;

    @Mock
    private ITreatmentProjectService treatmentProjectService;

    @InjectMocks
    private PaymentServiceImpl paymentService;

    private DrugVo mockDrugVo;
    private TreatmentProjectVo mockProjectVo;

    @BeforeEach
    void setUp() {
        // 模拟药品数据
        mockDrugVo = new DrugVo();
        mockDrugVo.setId(1L);
        mockDrugVo.setName("当归");
        mockDrugVo.setCode("DG001");
        mockDrugVo.setUnit("克");
        mockDrugVo.setPrice(1250L); // 12.5元，存储为分

        // 模拟诊疗项目数据
        mockProjectVo = new TreatmentProjectVo();
        mockProjectVo.setId(1L);
        mockProjectVo.setName("针灸");
        mockProjectVo.setCode("ZJ001");
        mockProjectVo.setUnit("次");
        mockProjectVo.setPrice(5000L); // 50元，存储为分
    }

    @Test
    void testCalculatePaymentDetailsFromBo_WithTcmPrescription() {
        // 准备测试数据
        MedicalRecordBo medicalRecordBo = new MedicalRecordBo();
        medicalRecordBo.setPatientId(1L);
        medicalRecordBo.setPatientName("张三");

        // 中药饮片处方数据
        TcmDrugItemBo drugItem = new TcmDrugItemBo();
        drugItem.setId("1");
        drugItem.setAmount(BigDecimal.valueOf(10)); // 10克

        TcmPrescriptionDataBo tcmData = new TcmPrescriptionDataBo();
        tcmData.setTableData(Arrays.asList(drugItem));
        medicalRecordBo.setTcmPrescriptionData(tcmData);

        // 支付信息
        PaymentBo payment = new PaymentBo();
        payment.setPaymentMethod("cash");
        payment.setRemark("测试支付");
        medicalRecordBo.setPayment(payment);

        // Mock 服务调用
        when(drugService.queryByIds(Arrays.asList(1L))).thenReturn(Arrays.asList(mockDrugVo));

        // 执行测试
        paymentService.savePayment(medicalRecordBo);

        // 验证
        verify(drugService, times(1)).queryByIds(Arrays.asList(1L));

        // 验证支付详情计算正确
        List<PaymentDetailBo> details = payment.getPaymentDetailList();
        assertNotNull(details);
        assertEquals(1, details.size());

        PaymentDetailBo detail = details.get(0);
        assertEquals("当归", detail.getItemName());
        assertEquals("DG001", detail.getItemCode());
        assertEquals("克", detail.getUnit());
        assertEquals(10L, detail.getQuantity());
        assertEquals(1250L, detail.getPrice()); // 12.5元 = 1250分
        assertEquals(12500L, detail.getAmount()); // 10 * 1250 = 12500分
    }

    @Test
    void testCalculatePaymentDetailsFromBo_WithTreatmentProject() {
        // 准备测试数据
        MedicalRecordBo medicalRecordBo = new MedicalRecordBo();
        medicalRecordBo.setPatientId(1L);
        medicalRecordBo.setPatientName("李四");

        // 诊疗项目数据
        TreatmentProjectItemBo projectItem = new TreatmentProjectItemBo();
        projectItem.setId("1");
        projectItem.setAmount(BigDecimal.valueOf(2)); // 2次

        TreatmentProjectDataBo projectData = new TreatmentProjectDataBo();
        projectData.setTableData(Arrays.asList(projectItem));
        medicalRecordBo.setTreatmentProjectData(projectData);

        // 支付信息
        PaymentBo payment = new PaymentBo();
        payment.setPaymentMethod("wechat");
        payment.setRemark("测试支付");
        medicalRecordBo.setPayment(payment);

        // Mock 服务调用
        when(treatmentProjectService.queryByIds(Arrays.asList(1L))).thenReturn(Arrays.asList(mockProjectVo));

        // 执行测试
        paymentService.savePayment(medicalRecordBo);

        // 验证
        verify(treatmentProjectService, times(1)).queryByIds(Arrays.asList(1L));

        // 验证支付详情计算正确
        List<PaymentDetailBo> details = payment.getPaymentDetailList();
        assertNotNull(details);
        assertEquals(1, details.size());

        PaymentDetailBo detail = details.get(0);
        assertEquals("针灸", detail.getItemName());
        assertEquals("ZJ001", detail.getItemCode());
        assertEquals("次", detail.getUnit());
        assertEquals(2L, detail.getQuantity());
        assertEquals(5000L, detail.getPrice()); // 50元 = 5000分
        assertEquals(10000L, detail.getAmount()); // 2 * 5000 = 10000分
    }

    @Test
    void testCalculatePaymentDetailsFromBo_WithInvalidDrugId() {
        // 准备测试数据
        MedicalRecordBo medicalRecordBo = new MedicalRecordBo();

        // 中药饮片处方数据 - 使用不存在的药品ID
        TcmDrugItemBo drugItem = new TcmDrugItemBo();
        drugItem.setId("999"); // 不存在的ID
        drugItem.setAmount(BigDecimal.valueOf(10));

        TcmPrescriptionDataBo tcmData = new TcmPrescriptionDataBo();
        tcmData.setTableData(Arrays.asList(drugItem));
        medicalRecordBo.setTcmPrescriptionData(tcmData);

        PaymentBo payment = new PaymentBo();
        medicalRecordBo.setPayment(payment);

        // Mock 服务调用返回空列表（药品不存在）
        when(drugService.queryByIds(Arrays.asList(999L))).thenReturn(Collections.emptyList());

        // 执行测试
        paymentService.savePayment(medicalRecordBo);

        // 验证：不存在的药品应该被过滤掉
        List<PaymentDetailBo> details = payment.getPaymentDetailList();
        assertNotNull(details);
        assertEquals(0, details.size());
    }
}
